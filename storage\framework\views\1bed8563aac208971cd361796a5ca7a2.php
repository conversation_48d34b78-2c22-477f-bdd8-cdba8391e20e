<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('IT Ticket Details')); ?>

<?php $__env->startSection('css_links'); ?>
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    .btn-block {
        width: 100%;
    }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('IT Ticket Details')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('IT Ticket')); ?></li>
    <li class="breadcrumb-item">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['IT Ticket Update', 'IT Ticket Delete'])): ?>
            <a href="<?php echo e(route('administration.ticket.it_ticket.index')); ?>"><?php echo e(__('All Tickets')); ?></a>
        <?php elseif (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['IT Ticket Read', 'IT Ticket Create'])): ?>
            <a href="<?php echo e(route('administration.ticket.it_ticket.my')); ?>"><?php echo e(__('My Tickets')); ?></a>
        <?php endif; ?>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('IT Ticket Details')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <b class="text-primary"><?php echo e($itTicket->creator->employee->alias_name. '\'s '); ?></b>
                    <?php echo e(__('IT Ticket Details')); ?>

                </h5>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['IT Ticket Update', 'IT Ticket Delete'])): ?>
                    <?php if($itTicket->status === 'Pending'): ?>
                        <div class="card-header-elements ms-auto">
                            <?php if($itTicket->status === 'Pending' && $itTicket->creator_id == auth()->user()->id): ?>
                                <a href="<?php echo e(route('administration.ticket.it_ticket.edit', ['it_ticket' => $itTicket])); ?>" class="btn btn-sm btn-info me-2 confirm-info" title="Edit & Update?">
                                    <span class="tf-icon ti ti-edit ti-xs"></span>
                                    <span class="me-1">Edit</span>
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('administration.ticket.it_ticket.mark.running', ['it_ticket' => $itTicket])); ?>" class="btn btn-sm btn-primary confirm-primary" title="Start Working On This Ticket?">
                                <span class="me-1">Proceed</span>
                                <span class="tf-icon ti ti-arrow-right ti-xs"></span>
                            </a>
                        </div>
                    <?php elseif($itTicket->status === 'Running'): ?>
                        <div class="card-header-elements ms-auto">
                            <button type="button" data-bs-toggle="modal" data-bs-target="#statusUpdateModal" class="btn btn-sm btn-primary" title="Mark as Solved / Canceled">
                                <span class="tf-icon ti ti-check ti-xs me-1"></span>
                                Update Ticket Status
                            </button>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="row justify-content-left">
                    <div class="col-md-6">
                        <?php echo $__env->make('administration.ticket.it_ticket.partials._information', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <div class="col-md-6">
                        <?php echo $__env->make('administration.ticket.it_ticket.partials._description', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <?php if(isset($itTicket->solver_note)): ?>
                            <?php echo $__env->make('administration.ticket.it_ticket.partials._note', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>

                        <?php echo $__env->make('administration.ticket.it_ticket.partials._comment', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php if($itTicket->status === 'Running'): ?>
    
    <?php echo $__env->make('administration.ticket.it_ticket.modals.status_update', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    
    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });

            $('.time-picker').flatpickr({
                enableTime: true,
                noCalendar: true,
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/ticket/it_ticket/show.blade.php ENDPATH**/ ?>