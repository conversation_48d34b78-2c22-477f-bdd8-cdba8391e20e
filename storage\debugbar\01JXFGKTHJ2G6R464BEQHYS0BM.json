{"__meta": {"id": "01JXFGKTHJ2G6R464BEQHYS0BM", "datetime": "2025-06-11 18:49:34", "utime": **********.772677, "method": "GET", "uri": "/attendance/export?user_id=7&created_month_year=June%202025&filter_attendance=true", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749646165.948592, "end": **********.772725, "duration": 8.824133157730103, "duration_str": "8.82s", "measures": [{"label": "Booting", "start": 1749646165.948592, "relative_start": 0, "end": **********.745985, "relative_end": **********.745985, "duration": 0.****************, "duration_str": "797ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.746006, "relative_start": 0.****************, "end": **********.772729, "relative_end": 3.814697265625e-06, "duration": 8.**************, "duration_str": "8.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.776064, "relative_start": 0.****************, "end": **********.781233, "relative_end": **********.781233, "duration": 0.0051691532135009766, "duration_str": "5.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.708924, "relative_start": 8.***************, "end": **********.765389, "relative_end": **********.765389, "duration": 0.*****************, "duration_str": "56.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET attendance/export", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Attendance Everything", "controller": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@export<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=318\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.attendance.export", "prefix": "/attendance", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=318\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Attendance/AttendanceController.php:318-379</a>"}, "queries": {"count": 19, "nb_statements": 19, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.025539999999999993, "accumulated_duration_str": "25.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.846505, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 13.939}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.861179, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 13.939, "width_percent": 2.467}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.888018, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 16.406, "width_percent": 4.307}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.892267, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "blueorange", "explain": null, "start_percent": 20.713, "width_percent": 3.132}, {"sql": "select `name` from `users` where `users`.`id` = '7' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 341}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.905913, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:341", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 341}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=341", "ajax": false, "filename": "AttendanceController.php", "line": "341"}, "connection": "blueorange", "explain": null, "start_percent": 23.845, "width_percent": 3.328}, {"sql": "select `attendances`.`id`, `attendances`.`user_id`, `attendances`.`employee_shift_id`, `attendances`.`clock_in_date`, `attendances`.`clock_in`, `attendances`.`clock_out`, `attendances`.`total_time`, `attendances`.`total_adjusted_time`, `attendances`.`type` from `attendances` inner join `users` on `users`.`id` = `attendances`.`user_id` where `attendances`.`user_id` = '7' and year(`attendances`.`clock_in`) = 2025 and month(`attendances`.`clock_in`) = '06' and `attendances`.`deleted_at` is null order by `users`.`name` asc", "type": "query", "params": [], "bindings": ["7", 2025, "06"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 367}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9094882, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:367", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=367", "ajax": false, "filename": "AttendanceController.php", "line": "367"}, "connection": "blueorange", "explain": null, "start_percent": 27.173, "width_percent": 5.208}, {"sql": "select `id`, `name` from `users` where `users`.`id` in (7) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 367}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.914545, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:367", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=367", "ajax": false, "filename": "AttendanceController.php", "line": "367"}, "connection": "blueorange", "explain": null, "start_percent": 32.381, "width_percent": 2.662}, {"sql": "select `id`, `start_time`, `end_time` from `employee_shifts` where `employee_shifts`.`id` in (7) and `employee_shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 367}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.919171, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:367", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=367", "ajax": false, "filename": "AttendanceController.php", "line": "367"}, "connection": "blueorange", "explain": null, "start_percent": 35.043, "width_percent": 8.888}, {"sql": "select * from `employees` where `employees`.`user_id` = 7 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 25}, {"index": 32, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 33, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 34, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.344822, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 43.931, "width_percent": 6.382}, {"sql": "select SEC_TO_TIME(SUM(TIME_TO_SEC(over_break))) as total_over_break from `daily_breaks` where `daily_breaks`.`attendance_id` = 7451 and `daily_breaks`.`attendance_id` is not null and `break_out_at` is not null and `daily_breaks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7451], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, {"index": 26, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 31}, {"index": 31, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 33, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.3552492, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:87", "source": {"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=87", "ajax": false, "filename": "AttendanceAccessors.php", "line": "87"}, "connection": "blueorange", "explain": null, "start_percent": 50.313, "width_percent": 9.319}, {"sql": "select sum(`total_time`) as aggregate from `penalties` where `penalties`.`attendance_id` = 7451 and `penalties`.`attendance_id` is not null and `penalties`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7451], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, {"index": 25, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 32}, {"index": 30, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 31, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.3623538, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:101", "source": {"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=101", "ajax": false, "filename": "AttendanceAccessors.php", "line": "101"}, "connection": "blueorange", "explain": null, "start_percent": 59.632, "width_percent": 4.464}, {"sql": "select SEC_TO_TIME(SUM(TIME_TO_SEC(over_break))) as total_over_break from `daily_breaks` where `daily_breaks`.`attendance_id` = 7550 and `daily_breaks`.`attendance_id` is not null and `break_out_at` is not null and `daily_breaks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7550], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, {"index": 26, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 31}, {"index": 31, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 33, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.366413, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:87", "source": {"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=87", "ajax": false, "filename": "AttendanceAccessors.php", "line": "87"}, "connection": "blueorange", "explain": null, "start_percent": 64.096, "width_percent": 4.816}, {"sql": "select sum(`total_time`) as aggregate from `penalties` where `penalties`.`attendance_id` = 7550 and `penalties`.`attendance_id` is not null and `penalties`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7550], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, {"index": 25, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 32}, {"index": 30, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 31, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.3697388, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:101", "source": {"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=101", "ajax": false, "filename": "AttendanceAccessors.php", "line": "101"}, "connection": "blueorange", "explain": null, "start_percent": 68.912, "width_percent": 4.424}, {"sql": "select SEC_TO_TIME(SUM(TIME_TO_SEC(over_break))) as total_over_break from `daily_breaks` where `daily_breaks`.`attendance_id` = 7635 and `daily_breaks`.`attendance_id` is not null and `break_out_at` is not null and `daily_breaks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7635], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, {"index": 26, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 31}, {"index": 31, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 33, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.373874, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:87", "source": {"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=87", "ajax": false, "filename": "AttendanceAccessors.php", "line": "87"}, "connection": "blueorange", "explain": null, "start_percent": 73.336, "width_percent": 5.756}, {"sql": "select sum(`total_time`) as aggregate from `penalties` where `penalties`.`attendance_id` = 7635 and `penalties`.`attendance_id` is not null and `penalties`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7635], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, {"index": 25, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 32}, {"index": 30, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 31, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.377103, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:101", "source": {"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=101", "ajax": false, "filename": "AttendanceAccessors.php", "line": "101"}, "connection": "blueorange", "explain": null, "start_percent": 79.092, "width_percent": 3.446}, {"sql": "select SEC_TO_TIME(SUM(TIME_TO_SEC(over_break))) as total_over_break from `daily_breaks` where `daily_breaks`.`attendance_id` = 7902 and `daily_breaks`.`attendance_id` is not null and `break_out_at` is not null and `daily_breaks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7902], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, {"index": 26, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 31}, {"index": 31, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 33, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.3800778, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:87", "source": {"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=87", "ajax": false, "filename": "AttendanceAccessors.php", "line": "87"}, "connection": "blueorange", "explain": null, "start_percent": 82.537, "width_percent": 3.72}, {"sql": "select sum(`total_time`) as aggregate from `penalties` where `penalties`.`attendance_id` = 7902 and `penalties`.`attendance_id` is not null and `penalties`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7902], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, {"index": 25, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 32}, {"index": 30, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 31, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.3833568, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:101", "source": {"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=101", "ajax": false, "filename": "AttendanceAccessors.php", "line": "101"}, "connection": "blueorange", "explain": null, "start_percent": 86.257, "width_percent": 4.464}, {"sql": "select SEC_TO_TIME(SUM(TIME_TO_SEC(over_break))) as total_over_break from `daily_breaks` where `daily_breaks`.`attendance_id` = 7970 and `daily_breaks`.`attendance_id` is not null and `break_out_at` is not null and `daily_breaks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7970], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, {"index": 26, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 31}, {"index": 31, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 33, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.387601, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:87", "source": {"index": 20, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=87", "ajax": false, "filename": "AttendanceAccessors.php", "line": "87"}, "connection": "blueorange", "explain": null, "start_percent": 90.72, "width_percent": 3.602}, {"sql": "select sum(`total_time`) as aggregate from `penalties` where `penalties`.`attendance_id` = 7970 and `penalties`.`attendance_id` is not null and `penalties`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7970], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, {"index": 25, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 32}, {"index": 30, "namespace": null, "name": "app/Exports/Administration/Attendance/AttendanceExport.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Exports\\Administration\\Attendance\\AttendanceExport.php", "line": 23}, {"index": 31, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 508}, {"index": 32, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 212}], "start": 1749646171.390745, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "AttendanceAccessors.php:101", "source": {"index": 19, "namespace": null, "name": "app/Models/Attendance/Accessors/AttendanceAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Attendance\\Accessors\\AttendanceAccessors.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAccessors%2FAttendanceAccessors.php&line=101", "ajax": false, "filename": "AttendanceAccessors.php", "line": "101"}, "connection": "blueorange", "explain": null, "start_percent": 94.323, "width_percent": 5.677}]}, "models": {"data": {"App\\Models\\Attendance\\Attendance": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAttendance.php&line=1", "ajax": false, "filename": "Attendance.php", "line": "?"}}, "App\\Models\\DailyBreak\\DailyBreak": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FDailyBreak%2FDailyBreak.php&line=1", "ajax": false, "filename": "DailyBreak.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\EmployeeShift\\EmployeeShift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FEmployeeShift%2FEmployeeShift.php&line=1", "ajax": false, "filename": "EmployeeShift.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => Attendance Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-69159617 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Attendance Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69159617\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.899758, "xdebug_link": null}]}, "session": {"_token": "VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/attendance/all?created_month_year=June%202025&filter_attendance=true&type=&user_id=7\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1749549177\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/attendance/export?created_month_year=June%202025&filter_attendance=true&user...", "action_name": "administration.attendance.export", "controller_action": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@export", "uri": "GET attendance/export", "controller": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@export<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=318\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/attendance", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=318\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Attendance/AttendanceController.php:318-379</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Attendance Everything", "duration": "8.83s", "peak_memory": "56MB", "response": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-553606666 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>created_month_year</span>\" => \"<span class=sf-dump-str title=\"9 characters\">June 2025</span>\"\n  \"<span class=sf-dump-key>filter_attendance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553606666\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-737595743 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-737595743\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-27109899 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"106 characters\">https://blueorange.test/attendance/all?user_id=7&amp;created_month_year=June+2025&amp;type=&amp;filter_attendance=true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6IjdqaVZHUjkvYlNwNE9aMkhTNk8xSXc9PSIsInZhbHVlIjoiaERzQUZLb094aFc4NHBDbVB6OUhlcStmNmlSN0JLTXRVN0lEODJhenhJNlU0MkFIdU10ZU5pbXVjaXJUL2JETGcyQjFCV2Vla3JiTVZ2K3BYcEtpVnJmWUxaMVl1aGdNSEtCYU5OK2k3RE5KT1BsazVBWEptUGh2QzZhS21uamsiLCJtYWMiOiJmZDAwYzBhNjMyMjY5MWQ1Mzg4OWI3NTc1ZWY0ODMwYzVmOTE0ZWM3ZDVhODE5Yzc3OWMzMzE1ZDNhNjE0ZDEzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImFZVU5INVhXNmthSzU2RUtvdUxPa1E9PSIsInZhbHVlIjoiRGZNYk5zeEZ5aHdqbzFTdGR4b3NDTnozVGdsY1hnRTRnNHA2cWlhSzlYMjZ4Z2ozN0EwbW9NVzExVGMxUDl4NStDQkVtbnlLZkJRVDh3QzJsc2JrSW50ODNGYU9jVUp3ZFRyV3lScGZ6OW9tb1pya1BESlZGYit5QkpBYnZqWksiLCJtYWMiOiIxYmJhYzU3YThhMjY0OTVhNTRmZDBkN2FmOWNkZWI1YTA1MjFkYjA3ZjFlNGY5N2MzNmY4MjlkZmYwMGQ0NTM5IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6ImpIL0s3ai9aRHdiTW1jdzF3cFk1V3c9PSIsInZhbHVlIjoiTHA1ZTlWZlhpRTY5b3Qwc1FGR1k5SmN1b25OazdqMGJmVklzbWxGb2pqMGpCajdDbjdmZHlVSlpEd2tSNUZNTkp0bTJEWkJvbm5PeHhQTU8zWStGTVlZOW9ERkhrZ3p4WTVBanZSUHJaQ3RwM2lBK1JaempVc21CeTlNZ0RveUsiLCJtYWMiOiJhNDZmZDNjZDU5NTAxMjg1NDhiYjI2NjA1YTE4MmI0M2Q4ZjRlZGE1ODRlMGJlYWM4ZDA2M2E0NjczZmYwNTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27109899\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1517748246 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7c9W5IVjtxrSlF5Dn2HxdVzXDiWshrz2F6UdeoLs</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dFIY5PGHO9DJ7O3P9VP8I9MtkVpL8ReySUApdhKj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517748246\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1231691210 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 12:49:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 12:49:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-disposition</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"71 characters\">attachment; filename=attendances_backup_of_shabab_zahir_of_06_2025.xlsx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6989</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231691210\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1828065054 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"108 characters\">https://blueorange.test/attendance/all?created_month_year=June%202025&amp;filter_attendance=true&amp;type=&amp;user_id=7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749549177</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828065054\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/attendance/export?created_month_year=June%202025&filter_attendance=true&user...", "action_name": "administration.attendance.export", "controller_action": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@export"}, "badge": null}}