<?php
    switch ($leaveHistory->type) {
        case 'Earned':
            $typeBg = 'success';
            break;

        case 'Sick':
            $typeBg = 'warning';
            break;

        default:
            $typeBg = 'danger';
            break;
    }
?>

<!-- Status Modal -->
<div class="modal fade" data-bs-backdrop="static" id="rejectLeaveModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content p-3 p-md-5">
            <button type="button" class="btn-close btn-pinned" data-bs-dismiss="modal" aria-label="Close"></button>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h3 class="role-title mb-2">Reject Leave</h3>
                    <p class="text-muted">Reject the <b class="text-<?php echo e($typeBg); ?>"><?php echo e($leaveHistory->type); ?> Leave</b> request of <b class="text-primary"><?php echo e($leaveHistory->user->alias_name); ?></b></p>
                </div>
                <!-- Status form -->
                <form method="post" action="<?php echo e(route('administration.leave.history.reject', ['leaveHistory' => $leaveHistory])); ?>" enctype="multipart/form-data" class="row g-3" autocomplete="off" id="rejectLeaveForm">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="mb-3 col-12">
                        <label class="form-label">Rejection Reason <strong class="text-danger">*</strong></label>
                        <div name="reviewer_note" id="leaveRejectNoteEditor"><?php echo old('reviewer_note'); ?></div>
                        <textarea class="d-none" name="reviewer_note" id="leaveRejectNoteInput"><?php echo e(old('reviewer_note')); ?></textarea>
                        <?php $__errorArgs = ['reviewer_note'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <b class="text-danger"><?php echo e($message); ?></b>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-12 text-center mt-4">
                        <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                        <button type="submit" class="btn btn-danger me-sm-3 me-1">
                            <i class="ti ti-check"></i>
                            Reject Leave
                        </button>
                    </div>
                </form>
                <!--/ Status form -->
            </div>
        </div>
    </div>
</div>
<!--/ Status Modal -->
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/leave/modals/reject.blade.php ENDPATH**/ ?>