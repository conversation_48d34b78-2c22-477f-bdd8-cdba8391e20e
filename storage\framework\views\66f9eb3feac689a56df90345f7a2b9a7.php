<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Penalty Details')); ?>

<?php $__env->startSection('css_links'); ?>
    

    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css" integrity="sha512-ZKX+BvQihRJPA8CROKBhDNvoc2aDMOdAlcm7TUQY+35XYtrd3yh95QOOhsPDQY9QnKE0Wqag9y38OIgEvb88cA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
        .img-thumbnail {
            padding: 3px;
            border: 3px solid var(--bs-border-color);
            border-radius: 5px;
        }
        .file-thumbnail-container {
            width: 150px;
            height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
        }
        .file-thumbnail-container .file-name {
            max-width: 140px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Penalty Details')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Penalty')); ?></li>
    <li class="breadcrumb-item">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Penalty Everything', 'Penalty Update', 'Penalty Delete'])): ?>
            <a href="<?php echo e(route('administration.penalty.index')); ?>"><?php echo e(__('All Penalties')); ?></a>
        <?php elseif (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Penalty Read'])): ?>
            <a href="<?php echo e(route('administration.penalty.my')); ?>"><?php echo e(__('My Penalties')); ?></a>
        <?php endif; ?>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Penalty Details')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <span class="text-bold"><?php echo e($penalty->user->alias_name); ?>'s</span> Penalty Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row justify-content-left">
                    <div class="col-md-6">
                        <?php echo $__env->make('administration.penalty.includes.penalty_details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <div class="col-md-6">
                        <div class="card card-action mb-4">
                            <div class="card-header align-items-center pb-3 pt-3">
                                <h5 class="card-action-title mb-0">Penalty Reason</h5>
                            </div>
                            <div class="card-body">
                                <div class="penalty-reason">
                                    <?php echo $penalty->reason; ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if($penalty->files->count() > 0): ?>
                    <div class="row justify-content-center">
                        <div class="col-md-12">
                                <?php echo $__env->make('administration.penalty.includes.penalty_proof_files', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    

    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox.min.js" integrity="sha512-Ixzuzfxv1EqafeQlTCufWfaC6ful6WFqIz4G+dWvK0beHw0NVJwvCKSgafpy5gwNqKmgUfIBraVwkKI+Cz0SEQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            // Lightbox configuration
            lightbox.option({
                'resizeDuration': 200,
                'wrapAround': true,
                'albumLabel': "Image %1 of %2"
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/penalty/show.blade.php ENDPATH**/ ?>