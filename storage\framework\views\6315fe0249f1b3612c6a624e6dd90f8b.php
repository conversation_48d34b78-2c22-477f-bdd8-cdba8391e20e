<div class="card mb-4">
    <div class="card-body">
        <small class="card-text text-uppercase">Penalty Details</small>
        <dl class="row mt-3 mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-user"></i>
                <span class="fw-medium mx-2 text-heading">Employee:</span>
            </dt>
            <dd class="col-sm-8">
                <?php echo show_user_name_and_avatar($penalty->user, name: null); ?>

            </dd>
        </dl>
        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-calendar-pause"></i>
                <span class="fw-medium mx-2 text-heading">Date:</span>
            </dt>
            <dd class="col-sm-8">
                <a href="<?php echo e(route('administration.attendance.show', ['attendance' => $penalty->attendance])); ?>" target="_blank" class="text-primary text-bold" title="Click here to view attendance details">
                    <?php echo e(show_date($penalty->attendance->clock_in)); ?>

                </a>
            </dd>
        </dl>
        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-clock-pause"></i>
                <span class="fw-medium mx-2 text-heading">Total Time:</span>
            </dt>
            <dd class="col-sm-8">
                <span class="text-dark text-bold"><?php echo e($penalty->total_time_formatted); ?></span>
            </dd>
        </dl>
        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-chart-candle"></i>
                <span class="fw-medium mx-2 text-heading">Type:</span>
            </dt>
            <dd class="col-sm-8">
                <span class="text-bold text-danger"><?php echo e($penalty->type); ?><span>
            </dd>
        </dl>
        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-clock-check"></i>
                <span class="fw-medium mx-2 text-heading">Submitted At:</span>
            </dt>
            <dd class="col-sm-8">
                <span class="text-dark"><?php echo e(show_date_time($penalty->created_at)); ?></span>
            </dd>
        </dl>
        <?php if(isset($penalty->creator)): ?>
            <dl class="row mb-1">
                <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                    <i class="ti ti-user-cog"></i>
                    <span class="fw-medium mx-2 text-heading">Creator:</span>
                </dt>
                <dd class="col-sm-8">
                    <?php echo show_user_name_and_avatar($penalty->creator, name: null); ?>

                </dd>
            </dl>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/penalty/includes/penalty_details.blade.php ENDPATH**/ ?>