<div class="card mb-4">
    <div class="card-body">
        <small class="card-text text-uppercase">Leave History Details</small>
        <dl class="row mt-3 mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-hash"></i>
                <span class="fw-medium mx-2 text-heading">Requested Type:</span>
            </dt>
            <dd class="col-sm-8">
                <?php if($leaveHistory->type === 'Earned'): ?>
                    <span class="badge bg-success"><?php echo e(__('Earned Leave')); ?></span>
                <?php elseif($leaveHistory->type === 'Sick'): ?>
                    <span class="badge bg-warning"><?php echo e(__('Sick Leave')); ?></span>
                <?php else: ?>
                    <span class="badge bg-danger"><?php echo e(__('Casual Leave')); ?></span>
                <?php endif; ?>
            </dd>
        </dl>
        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-calendar-pause"></i>
                <span class="fw-medium mx-2 text-heading">For Date:</span>
            </dt>
            <dd class="col-sm-8">
                <span class="text-dark text-bold"><?php echo e(show_date($leaveHistory->date)); ?></span>
            </dd>
        </dl>
        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-clock-pause"></i>
                <span class="fw-medium mx-2 text-heading">Total Time:</span>
            </dt>
            <dd class="col-sm-8">
                <span class="text-dark text-bold"><?php echo e($leaveHistory->total_leave->forHumans()); ?></span>
            </dd>
        </dl>
        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-chart-candle"></i>
                <span class="fw-medium mx-2 text-heading">Status:</span>
            </dt>
            <dd class="col-sm-8">
                <?php if($leaveHistory->status === 'Approved'): ?>
                    <span class="badge bg-success"><?php echo e(__('Approved')); ?></span>
                <?php elseif($leaveHistory->status === 'Rejected'): ?>
                    <span class="badge bg-danger"><?php echo e(__('Rejected')); ?></span>
                <?php elseif($leaveHistory->status === 'Pending'): ?>
                    <span class="badge bg-primary"><?php echo e(__('Pending')); ?></span>
                <?php else: ?>
                    <span class="badge bg-danger"><?php echo e(__('Canceled')); ?></span>
                <?php endif; ?>

                <?php if(isset($leaveHistory->is_paid_leave)): ?>
                    <?php switch($leaveHistory->is_paid_leave):
                        case (true): ?>
                            <span class="badge bg-success"><?php echo e(__('Paid')); ?></span>
                            <?php break; ?>
                        <?php default: ?>
                            <span class="badge bg-danger"><?php echo e(__('Unpaid')); ?><span>
                    <?php endswitch; ?>
                <?php endif; ?>
            </dd>
        </dl>
        <?php if(isset($leaveHistory->reviewed_by)): ?>
            <dl class="row mb-1">
                <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                    <i class="ti ti-user-cog"></i>
                    <span class="fw-medium mx-2 text-heading">Reviewed By:</span>
                </dt>
                <dd class="col-sm-8">
                    <?php echo show_user_name_and_avatar($leaveHistory->reviewer, role: null); ?>

                </dd>
            </dl>
        <?php endif; ?>
        <?php if(isset($leaveHistory->reviewed_at)): ?>
            <dl class="row mb-1">
                <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                    <i class="ti ti-clock-check"></i>
                    <span class="fw-medium mx-2 text-heading">Reviewed At:</span>
                </dt>
                <dd class="col-sm-8">
                    <span class="text-dark"><?php echo e(show_date_time($leaveHistory->reviewed_at)); ?></span>
                </dd>
            </dl>
        <?php endif; ?>
    </div>

    <?php if(!is_null($leaveHistory->reviewer_note) && ($leaveHistory->status === 'Rejected' || $leaveHistory->status === 'Canceled')): ?>
        <div class="card-body">
            <small class="card-text text-uppercase">Reviewer Note (<b class="text-dar"><?php echo e($leaveHistory->status); ?> Reason</b>)</small>
            <dl class="row mt-3 mb-1">
                <dd class="col-12">
                    <?php echo $leaveHistory->reviewer_note; ?>

                </dd>
            </dl>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/leave/includes/leave_history_details.blade.php ENDPATH**/ ?>