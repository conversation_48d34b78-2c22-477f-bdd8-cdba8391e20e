<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Leave')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Leaves')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Leave')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All Leaves')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <form action="<?php echo e(route('administration.leave.history.index')); ?>" method="get" autocomplete="off">
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-3">
                            <label for="team_leader_id" class="form-label"><?php echo e(__('Select Team Leader')); ?></label>
                            <select name="team_leader_id" id="team_leader_id" class="select2 form-select <?php $__errorArgs = ['team_leader_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->team_leader_id) ? 'selected' : ''); ?>><?php echo e(__('Select Team Leader')); ?></option>
                                <?php $__currentLoopData = $teamLeaders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $leader): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($leader->id); ?>" <?php echo e($leader->id == request()->team_leader_id ? 'selected' : ''); ?>>
                                        <?php echo e(get_employee_name($leader)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['team_leader_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="mb-3 col-md-3">
                            <label for="user_id" class="form-label"><?php echo e(__('Select Employee')); ?></label>
                            <select name="user_id" id="user_id" class="select2 form-select <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->user_id) ? 'selected' : ''); ?>><?php echo e(__('Select Employee')); ?></option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($user->id); ?>" <?php echo e($user->id == request()->user_id ? 'selected' : ''); ?>>
                                        <?php echo e(get_employee_name($user)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="mb-3 col-md-2">
                            <label class="form-label"><?php echo e(__('Leaves Of')); ?></label>
                            <input type="text" name="leave_month_year" value="<?php echo e(request()->leave_month_year ?? old('leave_month_year')); ?>" class="form-control month-year-picker" placeholder="MM yyyy" tabindex="-1"/>
                            <?php $__errorArgs = ['leave_month_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-2">
                            <label for="type" class="form-label"><?php echo e(__('Select Leave Type')); ?></label>
                            <select name="type" id="type" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default">
                                <option value="" <?php echo e(is_null(request()->type) ? 'selected' : ''); ?>><?php echo e(__('Select Type')); ?></option>
                                <option value="Earned" <?php echo e(request()->type == 'Earned' ? 'selected' : ''); ?>><?php echo e(__('Earned Leave')); ?></option>
                                <option value="Sick" <?php echo e(request()->type == 'Sick' ? 'selected' : ''); ?>><?php echo e(__('Sick Leave')); ?></option>
                                <option value="Casual" <?php echo e(request()->type == 'Casual' ? 'selected' : ''); ?>><?php echo e(__('Casual Leave')); ?></option>
                            </select>
                            <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-2">
                            <label for="status" class="form-label"><?php echo e(__('Select Status')); ?></label>
                            <select name="status" id="status" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default">
                                <option value="" <?php echo e(is_null(request()->status) ? 'selected' : ''); ?>><?php echo e(__('Select status')); ?></option>
                                <option value="Pending" <?php echo e(request()->status == 'Pending' ? 'selected' : ''); ?>><?php echo e(__('Pending')); ?></option>
                                <option value="Approved" <?php echo e(request()->status == 'Approved' ? 'selected' : ''); ?>><?php echo e(__('Approved')); ?></option>
                                <option value="Rejected" <?php echo e(request()->status == 'Rejected' ? 'selected' : ''); ?>><?php echo e(__('Rejected')); ?></option>
                                <option value="Canceled" <?php echo e(request()->status == 'Canceled' ? 'selected' : ''); ?>><?php echo e(__('Canceled')); ?></option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div> 
                    
                    <div class="col-md-12 text-end">
                        <?php if(request()->user_id || request()->leave_month_year || request()->type): ?> 
                            <a href="<?php echo e(route('administration.leave.history.index')); ?>" class="btn btn-danger confirm-warning">
                                <span class="tf-icon ti ti-refresh ti-xs me-1"></span>
                                <?php echo e(__('Reset Filters')); ?>

                            </a>
                        <?php endif; ?>
                        <button type="submit" name="filter_leaves" value="true" class="btn btn-primary">
                            <span class="tf-icon ti ti-filter ti-xs me-1"></span>
                            <?php echo e(__('Filter Leaves')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </form>        
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <span><?php echo e(request()->type ?? request()->type); ?></span>
                    <span>Leaves</span>
                    <span>of</span>
                    <span class="text-bold"><?php echo e(request()->user_id ? show_user_data(request()->user_id, 'name') : 'All Users'); ?></span>
                    <sup>(<b>Month: </b> <?php echo e(request()->leave_month_year ? request()->leave_month_year : date('F Y')); ?>)</sup>
                </h5>
        
                <div class="card-header-elements ms-auto">
                    <?php if($leaves->count() > 0): ?>
                        <a href="<?php echo e(route('administration.leave.history.export', [
                            'user_id' => request('user_id'), 
                            'leave_month_year' => request('leave_month_year'),
                            'type' => request('type'),
                            'filter_leaves' => request('filter_leaves')
                        ])); ?>" target="_blank" class="btn btn-sm btn-dark">
                            <span class="tf-icon ti ti-download me-1"></span>
                            <?php echo e(__('Download')); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Employee</th>
                                <th>Date</th>
                                <th>Total Leave</th>
                                <th>Status</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $leaves; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $leave): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                <tr>
                                    <th>#<?php echo e(serial($leaves, $key)); ?></th>
                                    <td>
                                        <?php echo show_user_name_and_avatar($leave->user, role: null); ?>

                                    </td>
                                    <td>
                                        <?php
                                            switch ($leave->type) {
                                                case 'Earned':
                                                    $typeBg = 'success';
                                                    break;
                                                
                                                case 'Sick':
                                                    $typeBg = 'warning';
                                                    break;
                                                
                                                default:
                                                    $typeBg = 'danger';
                                                    break;
                                            }
                                        ?>
                                        <?php echo e(show_date($leave->date)); ?>

                                        <br>
                                        <small class="badge bg-label-<?php echo e($typeBg); ?>" title="Requested Leave Type"><?php echo e($leave->type); ?> Leave</small>
                                    </td>
                                    <td>
                                        <?php if(!is_null($leave->is_paid_leave)): ?>
                                            <?php if($leave->is_paid_leave == true): ?>
                                                <small class="badge bg-success">Paid</small>
                                            <?php else: ?>
                                                <small class="badge bg-danger">Unpaid</small>
                                            <?php endif; ?>
                                            <br>
                                        <?php endif; ?>
                                        <span class="text-bold"><?php echo e($leave->total_leave->forHumans()); ?></span>
                                    </td>
                                    <td>
                                        <?php
                                            switch ($leave->status) {
                                                case 'Pending':
                                                    $statusBg = 'primary';
                                                    break;
                                                
                                                case 'Approved':
                                                    $statusBg = 'success';
                                                    break;
                                                
                                                default:
                                                    $statusBg = 'danger';
                                                    break;
                                            }
                                        ?>
                                        <span class="badge bg-<?php echo e($statusBg); ?>"><?php echo e($leave->status); ?></span>
                                        <?php if(!is_null($leave->reviewed_by)): ?>
                                            <br>
                                            <a href="<?php echo e(route('administration.settings.user.show.profile', ['user' => $leave->reviewer])); ?>" target="_blank" class="text-bold text-primary" title="Reviewed By">
                                                <?php echo e($leave->reviewer->first_name . ' (' . $leave->reviewer->employee->alias_name . ')'); ?>

                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <a href="<?php echo e(route('administration.leave.history.show', ['leaveHistory' => $leave])); ?>" class="btn btn-sm btn-icon btn-primary item-edit" data-bs-toggle="tooltip" title="Show Details">
                                            <i class="ti ti-info-hexagon"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>        
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
    
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });

            $('.month-year-picker').datepicker({
                format: 'MM yyyy',         // Display format to show full month name and year
                minViewMode: 'months',     // Only allow month selection
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/leave/index.blade.php ENDPATH**/ ?>