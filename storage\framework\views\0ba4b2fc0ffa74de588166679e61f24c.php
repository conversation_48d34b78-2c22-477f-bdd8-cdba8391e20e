<?php $__env->startSection('email_title'); ?>
    <?php if($user->id === $penalty->user_id): ?>
        <span style="text-align: center;">Penalty Assigned by <b><?php echo e($penalty->creator->alias_name); ?></b></span>
    <?php else: ?>
        <span style="text-align: center;">Team Member Penalty: <b><?php echo e($penalty->user->alias_name); ?></b> by <b><?php echo e($penalty->creator->alias_name); ?></b></span>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Start Content -->
<div>
    Hello <?php echo e($user->alias_name); ?>,
    <br><br>
    <?php if($user->id === $penalty->user_id): ?>
        A penalty has been assigned to you by <?php echo e($penalty->creator->alias_name); ?>.
    <?php else: ?>
        Your team member <strong><?php echo e($penalty->user->alias_name); ?></strong> has received a penalty assigned by <?php echo e($penalty->creator->alias_name); ?>.
    <?php endif; ?>
    <br><br>

    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <h3 style="color: #dc3545; margin-top: 0;">Penalty Details</h3>
        <p><strong>Type:</strong> <?php echo e($penalty->type); ?></p>
        <p><strong>Penalty Time:</strong> <?php echo e($penalty->total_time_formatted); ?></p>
        <p><strong>Date:</strong> <?php echo e(show_date_time($penalty->created_at)); ?></p>
    </div>

    <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
        <h4 style="color: #856404; margin-top: 0;">Related Attendance</h4>
        <p><strong>Date:</strong> <?php echo e(show_date($penalty->attendance->clock_in_date)); ?></p>
        <p><strong>Type:</strong> <?php echo e($penalty->attendance->type); ?></p>
        <p><strong>Clock In:</strong> <?php echo e(show_time($penalty->attendance->clock_in)); ?></p>
        <p><strong>Clock Out:</strong> <?php echo e($penalty->attendance->clock_out ? show_time($penalty->attendance->clock_out) : 'Ongoing'); ?></p>
    </div>

    <?php if($penalty->reason): ?>
    <div style="background-color: #f1f3f4; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <h4 style="color: #495057; margin-top: 0;">Reason</h4>
        <p><?php echo $penalty->reason; ?></p>
    </div>
    <?php endif; ?>

    <br>
    You can view the full penalty details by clicking <a href="<?php echo e(route('administration.penalty.show', $penalty)); ?>"><strong>here</strong></a>.
    <br><br>

    <?php if($user->id === $penalty->user_id): ?>
        If you have any questions or concerns about this penalty, please contact your supervisor or HR department.
        <br><br>
    <?php endif; ?>

    Best regards,<br>
    <?php echo e(config('app.name')); ?> Team
</div>
<!-- End Content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.email.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/emails/administration/penalty/penalty_created.blade.php ENDPATH**/ ?>