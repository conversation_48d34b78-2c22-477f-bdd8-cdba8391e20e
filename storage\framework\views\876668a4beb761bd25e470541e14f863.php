<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Leave History Details')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/typography.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/katex.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/editor.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Leave History Details')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Leave History')); ?></li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.leave.history.my')); ?>"><?php echo e(__('My Leave Histories')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Leave History Details')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0"><a href="<?php echo e(route('administration.settings.user.leave_allowed.index', ['user' => $leaveHistory->user])); ?>" target="_blank" class="text-bold"><?php echo e($leaveHistory->user->alias_name); ?></a> Leave History's Details</h5>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Leave History Update', 'Leave History Delete'])): ?>
                    <?php if($leaveHistory->status === 'Pending'): ?>
                        <div class="card-header-elements ms-auto">
                            <button type="button" data-bs-toggle="modal" data-bs-target="#approveLeaveModal" class="btn btn-sm btn-success">
                                <span class="tf-icon ti ti-check ti-xs me-1"></span>
                                Approve
                            </button>
                            <button type="button" data-bs-toggle="modal" data-bs-target="#rejectLeaveModal" class="btn btn-sm btn-danger">
                                <span class="tf-icon ti ti-check ti-xs me-1"></span>
                                Reject
                            </button>
                        </div>
                    <?php endif; ?>
                    <?php if($leaveHistory->status === 'Approved'): ?>
                        <div class="card-header-elements ms-auto">
                            <button type="button" data-bs-toggle="modal" data-bs-target="#cancelLeaveModal" class="btn btn-sm btn-danger">
                                <span class="tf-icon ti ti-ban ti-xs me-1"></span>
                                <?php echo e(__('Cancel Leave')); ?>

                            </button>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="row justify-content-left">
                    <div class="col-md-6">
                        <?php echo $__env->make('administration.leave.includes.leave_history_details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <?php if($leaveHistory->status === 'Pending'): ?>
                            <?php echo $__env->make('administration.leave.includes.available_leaves', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-6">
                        <?php echo $__env->make('administration.leave.includes.leave_reason', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <?php if($leaveHistory->files->count() > 0): ?>
                            <?php echo $__env->make('administration.leave.includes.leave_proof_files', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php if($leaveHistory->status === 'Pending'): ?>
    
    <?php echo $__env->make('administration.leave.modals.approve', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    
    <?php echo $__env->make('administration.leave.modals.reject', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>

<?php if($leaveHistory->status === 'Approved'): ?>
    
    <?php echo $__env->make('administration.leave.modals.cancel', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/quill/katex.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/quill/quill.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });

            $('.time-picker').flatpickr({
                enableTime: true,
                noCalendar: true,
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            var fullToolbar = [
                [{ font: [] }, { size: [] }],
                ["bold", "italic", "underline", "strike"],
                [{ color: [] }, { background: [] }],
                ["link"],
                [{ header: "1" }, { header: "2" }, "blockquote"],
                [{ list: "ordered" }, { list: "bullet" }],
            ];

            var leaveRejectNoteEditor = new Quill("#leaveRejectNoteEditor", {
                bounds: "#leaveRejectNoteEditor",
                placeholder: "Ex: Completed the task.",
                modules: {
                    formula: true,
                    toolbar: fullToolbar,
                },
                theme: "snow",
            });

            // Set the editor content to the old reviewer_note if validation fails
            <?php if(old('reviewer_note')): ?>
                leaveRejectNoteEditor.root.innerHTML = <?php echo json_encode(old('reviewer_note')); ?>;
            <?php endif; ?>

            $('#rejectLeaveForm').on('submit', function() {
                $('#leaveRejectNoteInput').val(leaveRejectNoteEditor.root.innerHTML);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/leave/show.blade.php ENDPATH**/ ?>