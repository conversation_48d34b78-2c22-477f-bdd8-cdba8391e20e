<div class="card mt-3">
    <div class="card-body">
        <div class="d-flex">
            <small class="card-text text-uppercase">Penalties</small>
            <div class="ms-auto" style="margin-top: -5px;">
                <?php if($attendance->penalties->count() > 0): ?>
                    <small class="badge bg-danger" title="Total Penalty Time">
                        1h 30m
                    </small>
                <?php endif; ?>
            </div>
        </div>
        <ul class="timeline mb-0 pb-1 mt-4">
            <?php $__empty_1 = true; $__currentLoopData = $attendance->penalties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $penalty): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <li class="timeline-item ps-4 <?php echo e($loop->last ? 'border-transparent' : 'border-left-dashed pb-1'); ?>">
                    <span class="timeline-indicator-advanced timeline-indicator-danger">
                        <i class="ti ti-alert-triangle"></i>
                    </span>
                    <div class="timeline-event px-0 pb-0">
                        <div class="timeline-header">
                            <small class="text-capitalize fw-bold" title="Click To See Details">
                                <a href="<?php echo e(route('administration.penalty.show', ['penalty' => $penalty])); ?>" target="_blank" class="text-danger"><?php echo e($penalty->type); ?></a>
                            </small>
                        </div>
                        <small class="text-muted mb-0">
                            <?php echo e(show_date_time($penalty->created_at)); ?>

                        </small>
                        <h6 class="mb-1 mt-1">
                            <span class="badge bg-danger"><?php echo e($penalty->total_time_formatted); ?></span>
                        </h6>
                    </div>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="text-center text-bold text-muted fs-2">No Penalties</div>
            <?php endif; ?>
        </ul>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/attendance/partials/_penalties.blade.php ENDPATH**/ ?>