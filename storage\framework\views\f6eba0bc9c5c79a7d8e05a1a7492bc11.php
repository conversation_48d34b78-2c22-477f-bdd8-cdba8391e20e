<!-- Penalty Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Penalty Everything', 'Penalty Create', 'Penalty Update', 'Penalty Delete'])): ?>
    <li class="menu-item <?php echo e(request()->is('penalty*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-gavel"></i>
            <div data-i18n="Penalty"><?php echo e(__('Penalty')); ?></div>
        </a>
        <ul class="menu-sub">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Penalty Everything', 'Penalty Update', 'Penalty Delete'])): ?>
                <li class="menu-item <?php echo e(request()->is('penalty/all*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.penalty.index')); ?>" class="menu-link"><?php echo e(__('All Penalties')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Penalty Everything', 'Penalty Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('penalty/my*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.penalty.my')); ?>" class="menu-link"><?php echo e(__('My Penalties')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Penalty Everything', 'Penalty Create'])): ?>
                <li class="menu-item <?php echo e(request()->is('penalty/create*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.penalty.create')); ?>" class="menu-link"><?php echo e(__('Create Penalty')); ?></a>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php elseif (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(['Penalty Read'])): ?>
    <li class="menu-item <?php echo e(request()->is('penalty/my*') ? 'active' : ''); ?>">
        <a href="<?php echo e(route('administration.penalty.my')); ?>" class="menu-link">
            <i class="menu-icon tf-icons ti ti-gavel"></i>
            <div data-i18n="My Penalties"><?php echo e(__('My Penalties')); ?></div>
        </a>
    </li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/penalty.blade.php ENDPATH**/ ?>