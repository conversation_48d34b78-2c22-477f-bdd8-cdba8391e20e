{"__meta": {"id": "01JXFQE5A2GCTV5B0BSJ8RDES6", "datetime": "2025-06-11 20:48:49", "utime": **********.220091, "method": "POST", "uri": "/daily_work_update/store", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749653320.56268, "end": **********.220123, "duration": 8.657443046569824, "duration_str": "8.66s", "measures": [{"label": "Booting", "start": 1749653320.56268, "relative_start": 0, "end": **********.873195, "relative_end": **********.873195, "duration": 2.****************, "duration_str": "2.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.873224, "relative_start": 2.***************, "end": **********.220126, "relative_end": 2.86102294921875e-06, "duration": 6.***************, "duration_str": "6.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.922787, "relative_start": 2.***************, "end": **********.935489, "relative_end": **********.935489, "duration": 0.012701988220214844, "duration_str": "12.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.212773, "relative_start": 8.***************, "end": **********.213825, "relative_end": **********.213825, "duration": 0.0010519027709960938, "duration_str": "1.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST daily_work_update/store", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, check.employee.info, can:Daily Work Update Create", "controller": "App\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController@store<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=311\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.daily_work_update.store", "prefix": "/daily_work_update", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=311\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php:311-352</a>"}, "queries": {"count": 14, "nb_statements": 12, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03161, "accumulated_duration_str": "31.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.0686998, "duration": 0.00657, "duration_str": "6.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 20.785}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.170183, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 20.785, "width_percent": 2.689}, {"sql": "select * from `employees` where `employees`.`user_id` = 1 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "check.employee.info", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckEmployeeInformation.php", "line": 30}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 21}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 22}], "start": **********.188983, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "check.employee.info:30", "source": {"index": 21, "namespace": "middleware", "name": "check.employee.info", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckEmployeeInformation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FCheckEmployeeInformation.php&line=30", "ajax": false, "filename": "CheckEmployeeInformation.php", "line": "30"}, "connection": "blueorange", "explain": null, "start_percent": 23.474, "width_percent": 5.157}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2378771, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 28.63, "width_percent": 9.364}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.2461078, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "blueorange", "explain": null, "start_percent": 37.994, "width_percent": 6.928}, {"sql": "select count(*) as aggregate from `daily_work_updates` where `date` = '2025-06-11' and `user_id` = '1'", "type": "query", "params": [], "bindings": ["2025-06-11", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.341708, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "blueorange", "explain": null, "start_percent": 44.922, "width_percent": 7.181}, {"sql": "select `users`.*, `employee_team_leader`.`employee_id` as `pivot_employee_id`, `employee_team_leader`.`team_leader_id` as `pivot_team_leader_id`, `employee_team_leader`.`is_active` as `pivot_is_active`, `employee_team_leader`.`created_at` as `pivot_created_at`, `employee_team_leader`.`updated_at` as `pivot_updated_at` from `users` inner join `employee_team_leader` on `users`.`id` = `employee_team_leader`.`team_leader_id` where `employee_team_leader`.`employee_id` = 1 and `employee_team_leader`.`is_active` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 108}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 315}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.41735, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:108", "source": {"index": 16, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=108", "ajax": false, "filename": "UserAccessors.php", "line": "108"}, "connection": "blueorange", "explain": null, "start_percent": 52.104, "width_percent": 11.958}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 322}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.434835, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DailyWorkUpdateController.php:322", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 322}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=322", "ajax": false, "filename": "DailyWorkUpdateController.php", "line": "322"}, "connection": "blueorange", "explain": null, "start_percent": 64.062, "width_percent": 0}, {"sql": "insert into `daily_work_updates` (`user_id`, `team_leader_id`, `date`, `work_update`, `progress`, `note`, `updated_at`, `created_at`) values (1, 2, '2025-06-11 00:00:00', '<h2>What is Lorem Ipsum?</h2><p class=\\\"ql-align-justify\\\"><strong>Lorem Ipsum</strong>&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum</p><p><br></p>', '100', '<h2>Why do we use it?</h2><p class=\\\"ql-align-justify\\\">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using \\'Content here, content here\\', making it look like readable English. Many desktop publishing packages and web page editors now&nbsp;</p><p><br></p>', '2025-06-11 20:48:43', '2025-06-11 20:48:43')", "type": "query", "params": [], "bindings": [1, 2, "2025-06-11 00:00:00", "<h2>What is Lorem Ipsum?</h2><p class=\"ql-align-justify\"><strong>Lorem Ipsum</strong>&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum</p><p><br></p>", "100", "<h2>Why do we use it?</h2><p class=\"ql-align-justify\">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now&nbsp;</p><p><br></p>", "2025-06-11 20:48:43", "2025-06-11 20:48:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 323}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 322}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4400609, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "DailyWorkUpdateController.php:323", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 323}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=323", "ajax": false, "filename": "DailyWorkUpdateController.php", "line": "323"}, "connection": "blueorange", "explain": null, "start_percent": 64.062, "width_percent": 8.035}, {"sql": "insert into `file_media` (`file_name`, `file_path`, `mime_type`, `file_size`, `original_name`, `fileable_id`, `fileable_type`, `uploader_id`, `updated_at`, `created_at`) values ('wmremove-transformed.jpeg', 'daily_work_update/UID00000001/5QPQpnTBpPk2uEeK3JBJkdn9sk3DRJi5RNyF5btO.jpg', 'image/jpeg', 170867, 'wmremove-transformed.jpeg', 5563, 'App\\\\Models\\\\DailyWorkUpdate\\\\DailyWorkUpdate', 1, '2025-06-11 20:48:43', '2025-06-11 20:48:43')", "type": "query", "params": [], "bindings": ["wmremove-transformed.jpeg", "daily_work_update/UID00000001/5QPQpnTBpPk2uEeK3JBJkdn9sk3DRJi5RNyF5btO.jpg", "image/jpeg", 170867, "wmremove-transformed.jpeg", 5563, "App\\Models\\DailyWorkUpdate\\DailyWorkUpdate", 1, "2025-06-11 20:48:43", "2025-06-11 20:48:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 28}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 322}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.510005, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "FileMediaHelper.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FFileMediaHelper.php&line=28", "ajax": false, "filename": "FileMediaHelper.php", "line": "28"}, "connection": "blueorange", "explain": null, "start_percent": 72.097, "width_percent": 6.359}, {"sql": "insert into `notifications` (`id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('c0013b1f-87f5-498a-9a18-5481d70e5490', 'App\\\\Notifications\\\\Administration\\\\DailyWorkUpdate\\\\DailyWorkUpdateCreateNotification', '{\\\"url\\\":\\\"https:\\\\/\\\\/blueorange.test\\\\/daily_work_update\\\\/show\\\\/dmkVe5jJLX5690bG\\\",\\\"icon\\\":\\\"speakerphone\\\",\\\"title\\\":\\\"New Work Update Arrived\\\",\\\"message\\\":\\\"A New Work Update Has Been Submitted By Developer\\\"}', null, 2, 'App\\\\Models\\\\User', '2025-06-11 20:48:47', '2025-06-11 20:48:47')", "type": "query", "params": [], "bindings": ["c0013b1f-87f5-498a-9a18-5481d70e5490", "App\\Notifications\\Administration\\DailyWorkUpdate\\DailyWorkUpdateCreateNotification", "{\"url\":\"https:\\/\\/blueorange.test\\/daily_work_update\\/show\\/dmkVe5jJLX5690bG\",\"icon\":\"speakerphone\",\"title\":\"New Work Update Arrived\",\"message\":\"A New Work Update Has Been Submitted By Developer\"}", null, 2, "App\\Models\\User", "2025-06-11 20:48:47", "2025-06-11 20:48:47"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 106}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 101}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 79}], "start": **********.209634, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseChannel.php:19", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/Channels/DatabaseChannel.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\DatabaseChannel.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FChannels%2FDatabaseChannel.php&line=19", "ajax": false, "filename": "DatabaseChannel.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 78.456, "width_percent": 4.904}, {"sql": "select * from `employees` where `employees`.`user_id` = 2 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 344}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 322}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2590911, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "DailyWorkUpdateController.php:344", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=344", "ajax": false, "filename": "DailyWorkUpdateController.php", "line": "344"}, "connection": "blueorange", "explain": null, "start_percent": 83.36, "width_percent": 3.828}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"8efb3d22-3f85-41dc-a34e-5938539c3d39\\\",\\\"displayName\\\":\\\"App\\\\\\\\Mail\\\\\\\\Administration\\\\\\\\DailyWorkUpdate\\\\\\\\DailyWorkUpdateRequestMail\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Mail\\\\\\\\SendQueuedMailable\\\",\\\"command\\\":\\\"O:34:\\\\\\\"Illuminate\\\\\\\\Mail\\\\\\\\SendQueuedMailable\\\\\\\":15:{s:8:\\\\\\\"mailable\\\\\\\";O:66:\\\\\\\"App\\\\\\\\Mail\\\\\\\\Administration\\\\\\\\DailyWorkUpdate\\\\\\\\DailyWorkUpdateRequestMail\\\\\\\":4:{s:4:\\\\\\\"data\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:42:\\\\\\\"App\\\\\\\\Models\\\\\\\\DailyWorkUpdate\\\\\\\\DailyWorkUpdate\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:5563;s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:4:\\\\\\\"user\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:2;s:9:\\\\\\\"relations\\\\\\\";a:1:{i:0;s:8:\\\\\\\"employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;a:2:{s:4:\\\\\\\"name\\\\\\\";N;s:7:\\\\\\\"address\\\\\\\";s:26:\\\\\\\"<EMAIL>\\\\\\\";}}s:6:\\\\\\\"mailer\\\\\\\";s:4:\\\\\\\"smtp\\\\\\\";}s:5:\\\\\\\"tries\\\\\\\";N;s:7:\\\\\\\"timeout\\\\\\\";N;s:13:\\\\\\\"maxExceptions\\\\\\\";N;s:17:\\\\\\\"shouldBeEncrypted\\\\\\\";b:0;s:10:\\\\\\\"connection\\\\\\\";N;s:5:\\\\\\\"queue\\\\\\\";N;s:15:\\\\\\\"chainConnection\\\\\\\";N;s:10:\\\\\\\"chainQueue\\\\\\\";N;s:19:\\\\\\\"chainCatchCallbacks\\\\\\\";N;s:5:\\\\\\\"delay\\\\\\\";N;s:11:\\\\\\\"afterCommit\\\\\\\";N;s:10:\\\\\\\"middleware\\\\\\\";a:0:{}s:7:\\\\\\\"chained\\\\\\\";a:0:{}s:3:\\\\\\\"job\\\\\\\";N;}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"8efb3d22-3f85-41dc-a34e-5938539c3d39\",\"displayName\":\"App\\\\Mail\\\\Administration\\\\DailyWorkUpdate\\\\DailyWorkUpdateRequestMail\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Mail\\\\SendQueuedMailable\",\"command\":\"O:34:\\\"Illuminate\\\\Mail\\\\SendQueuedMailable\\\":15:{s:8:\\\"mailable\\\";O:66:\\\"App\\\\Mail\\\\Administration\\\\DailyWorkUpdate\\\\DailyWorkUpdateRequestMail\\\":4:{s:4:\\\"data\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:42:\\\"App\\\\Models\\\\DailyWorkUpdate\\\\DailyWorkUpdate\\\";s:2:\\\"id\\\";i:5563;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:4:\\\"user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:1:{i:0;s:8:\\\"employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"to\\\";a:1:{i:0;a:2:{s:4:\\\"name\\\";N;s:7:\\\"address\\\";s:26:\\\"<EMAIL>\\\";}}s:6:\\\"mailer\\\";s:4:\\\"smtp\\\";}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:13:\\\"maxExceptions\\\";N;s:17:\\\"shouldBeEncrypted\\\";b:0;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:3:\\\"job\\\";N;}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 59}], "start": **********.123195, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 87.188, "width_percent": 12.812}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 322}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.199045, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DailyWorkUpdateController.php:322", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController.php", "line": 322}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=322", "ajax": false, "filename": "DailyWorkUpdateController.php", "line": "322"}, "connection": "blueorange", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-927979006 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927979006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260498, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-726380331 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726380331\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.270734, "xdebug_link": null}]}, "session": {"_token": "VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/daily_work_update/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:13 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.padding\"\n    5 => \"alert.config.showConfirmButton\"\n    6 => \"alert.config.showCloseButton\"\n    7 => \"alert.config.timerProgressBar\"\n    8 => \"alert.config.customClass\"\n    9 => \"alert.config.toast\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config.position\"\n    12 => \"alert.config\"\n  ]\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1749549177\n]", "alert": "array:1 [\n  \"config\" => \"{\"title\":\"Daily Work Update Has Been Submitted Successfully.\",\"text\":\"\",\"timer\":\"5000\",\"width\":\"32rem\",\"padding\":\"1.25rem\",\"showConfirmButton\":false,\"showCloseButton\":true,\"timerProgressBar\":true,\"customClass\":{\"container\":null,\"popup\":null,\"header\":null,\"title\":null,\"closeButton\":null,\"icon\":null,\"image\":null,\"content\":null,\"input\":null,\"actions\":null,\"confirmButton\":null,\"cancelButton\":null,\"footer\":null},\"toast\":true,\"icon\":\"success\",\"position\":\"top-end\"}\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/daily_work_update/store", "action_name": "administration.daily_work_update.store", "controller_action": "App\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController@store", "uri": "POST daily_work_update/store", "controller": "App\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController@store<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=311\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/daily_work_update", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyWorkUpdate%2FDailyWorkUpdateController.php&line=311\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/DailyWorkUpdate/DailyWorkUpdateController.php:311-352</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, check.employee.info, can:Daily Work Update Create", "duration": "8.33s", "peak_memory": "38MB", "response": "Redirect to https://blueorange.test/daily_work_update/my", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2072815554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2072815554\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1966060654 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-11</span>\"\n  \"<span class=sf-dump-key>progress</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n  \"<span class=sf-dump-key>work_update</span>\" => \"<span class=sf-dump-str title=\"667 characters\">&lt;h2&gt;What is Lorem Ipsum?&lt;/h2&gt;&lt;p class=&quot;ql-align-justify&quot;&gt;&lt;strong&gt;Lorem Ipsum&lt;/strong&gt;&amp;nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>note_issue</span>\" => \"<span class=sf-dump-str title=\"438 characters\">&lt;h2&gt;Why do we use it?&lt;/h2&gt;&lt;p class=&quot;ql-align-justify&quot;&gt;It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using &#039;Content here, content here&#039;, making it look like readable English. Many desktop publishing packages and web page editors now&amp;nbsp;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966060654\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1090163443 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">172701</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryP5IApzoTg940e6kP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">https://blueorange.test/daily_work_update/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6ImpMRmhabWdSN2NJVG9QUUdLRmVNN1E9PSIsInZhbHVlIjoibnFXVVcyM3BIYVJ4RXhRNG1DTE4yUHhNeUoxbmEvL01ZOER0MkRmVFdETlE1Y3dxNjVxQ0hha1czR2ZPelBvajlRc1dvWGdsclBhZ1NKWEhsOVdTZW8xZVV2dFJta21SNGFqUXZ1Ukc1Nkt0NW9UbUxKNGs0VTI5d0tmbmVneTgiLCJtYWMiOiIwNjdhZmIxY2QxZDNhMDZmOTUzZmI1NzZlOWY0MTkyN2FkMDJlZWZiMTVmMjg5ZGI0OGRlZmU1NDY4YmU0ZWViIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Inp6Q0hZQ1UxZWVIN3NWenhBVFVmSUE9PSIsInZhbHVlIjoicE1jUHQyeDVHc2JYUnlWdEwrNFhHaUlMOWxIK1dqNFBiVlNPMGlwZDMxK1J4VlJ3WUc2eUt1a2ZUVys5QzRHN2txY2pZRW9KVFY5OEt2QjJpVkorL28rQ3ptaDVpV0VtWEpiYklsMjk5MmFpV0MzQjVFVEpsZmtuV0czSnlWRW8iLCJtYWMiOiI1MDMyMmEzODA0ZDgyZGNiN2YzNThjYTIzNDM1MjgwMDY0Y2YzOTJiZmExZjczODAwZThiZjQyNmU3OTExMTFlIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IjEyZFVpRUFSa0pvQ0lLZzQ0U3dQZWc9PSIsInZhbHVlIjoiTGtmMnYySy9KSzJ4bWJKTXprU0Mva3R4dnRQbHdqZEE4ZkVpb3JhYTl5alFaWU5SZHZpc0wvV1VtaDNqSGt3d2oya1JYWURyWkN2WlIvTVBPUmNyMmxEVjNVVXVSREU5TnhnZURWREdkVW5UM1c1WG45OGJRREh4UTZrblZ0N2kiLCJtYWMiOiJlNWJmYWM2NDFjZjI5OGY3NDUwYjQyNTc2MGRkMDI3NDllNTAzMTA1MWMwOWJjNWU0MDAwODQ1MGFlY2E4ODdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090163443\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-315199756 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7c9W5IVjtxrSlF5Dn2HxdVzXDiWshrz2F6UdeoLs</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dFIY5PGHO9DJ7O3P9VP8I9MtkVpL8ReySUApdhKj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-315199756\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2115521222 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 14:48:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">https://blueorange.test/daily_work_update/my</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115521222\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-393001200 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">https://blueorange.test/daily_work_update/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.toast</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"21 characters\">alert.config.position</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749549177</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"462 characters\">{&quot;title&quot;:&quot;Daily Work Update Has Been Submitted Successfully.&quot;,&quot;text&quot;:&quot;&quot;,&quot;timer&quot;:&quot;5000&quot;,&quot;width&quot;:&quot;32rem&quot;,&quot;padding&quot;:&quot;1.25rem&quot;,&quot;showConfirmButton&quot;:false,&quot;showCloseButton&quot;:true,&quot;timerProgressBar&quot;:true,&quot;customClass&quot;:{&quot;container&quot;:null,&quot;popup&quot;:null,&quot;header&quot;:null,&quot;title&quot;:null,&quot;closeButton&quot;:null,&quot;icon&quot;:null,&quot;image&quot;:null,&quot;content&quot;:null,&quot;input&quot;:null,&quot;actions&quot;:null,&quot;confirmButton&quot;:null,&quot;cancelButton&quot;:null,&quot;footer&quot;:null},&quot;toast&quot;:true,&quot;icon&quot;:&quot;success&quot;,&quot;position&quot;:&quot;top-end&quot;}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393001200\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/daily_work_update/store", "action_name": "administration.daily_work_update.store", "controller_action": "App\\Http\\Controllers\\Administration\\DailyWorkUpdate\\DailyWorkUpdateController@store"}, "badge": "302 Found"}}