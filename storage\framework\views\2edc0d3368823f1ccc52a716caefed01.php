<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('My Penalties')); ?>

<?php $__env->startSection('css_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    <style>
        .penalty-card {
            border: 1px solid #e7eaf3;
            border-radius: 12px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        .penalty-card:hover {
            border-color: #696cff;
            box-shadow: 0 4px 12px rgba(105, 108, 255, 0.15);
        }
        .penalty-type-badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
        .penalty-time-badge {
            font-size: 0.875rem;
            font-weight: 600;
        }
        .attendance-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
        }
        .no-penalties {
            text-align: center;
            padding: 60px 20px;
            color: #8592a3;
        }
        .no-penalties i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('My Penalties')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('My Penalties')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="ti ti-gavel me-2"></i><?php echo e(__('My Penalties')); ?>

                </h5>
                <div class="d-flex align-items-center">
                    <span class="badge bg-danger me-2"><?php echo e($penalties->count()); ?> <?php echo e(__('Total')); ?></span>
                    <?php if($penalties->count() > 0): ?>
                        <span class="badge bg-warning">
                            <?php echo e($penalties->sum('total_time')); ?> <?php echo e(__('Minutes Total')); ?>

                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <?php if($penalties->count() > 0): ?>
                    <div class="row">
                        <?php $__currentLoopData = $penalties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $penalty): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="penalty-card">
                                    <div class="card-body">
                                        <!-- Penalty Header -->
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <span class="badge bg-danger penalty-type-badge">
                                                <?php echo e($penalty->type); ?>

                                            </span>
                                            <span class="badge bg-dark penalty-time-badge">
                                                <?php echo e($penalty->total_time_formatted); ?>

                                            </span>
                                        </div>

                                        <!-- Attendance Information -->
                                        <div class="attendance-info mb-3">
                                            <h6 class="mb-2">
                                                <i class="ti ti-calendar me-1"></i><?php echo e(__('Attendance Details')); ?>

                                                <strong class="float-right"><?php echo e($penalty->attendance->type); ?></strong>
                                            </h6>
                                            <div class="row mt-3">
                                                <div class="col-12">
                                                    <small class="text-muted"><?php echo e(__('Date:')); ?></small><br>
                                                    <strong><?php echo e(show_date($penalty->attendance->clock_in_date)); ?></strong>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-6">
                                                    <small class="text-muted"><?php echo e(__('Clock In:')); ?></small><br>
                                                    <strong><?php echo e(show_time($penalty->attendance->clock_in)); ?></strong>
                                                </div>
                                                <div class="col-6 text-right">
                                                    <small class="text-muted"><?php echo e(__('Clock Out:')); ?></small><br>
                                                    <strong>
                                                        <?php echo e($penalty->attendance->clock_out ? show_time($penalty->attendance->clock_out) : __('Ongoing')); ?>

                                                    </strong>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Footer Information -->
                                        <div class="d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
                                            <div>
                                                <?php echo show_user_name_and_avatar($penalty->creator, name: null); ?>

                                            </div>
                                            <div class="text-end">
                                                <a href="<?php echo e(route('administration.penalty.show', $penalty)); ?>"
                                                   class="btn btn-sm btn-icon btn-outline-primary" target="_blank" title="<?php echo e(__('View Details')); ?>">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="no-penalties">
                        <i class="ti ti-mood-happy"></i>
                        <h4><?php echo e(__('No Penalties Found')); ?></h4>
                        <p class="mb-0"><?php echo e(__('You have no penalties recorded. Keep up the good work!')); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/penalty/my.blade.php ENDPATH**/ ?>