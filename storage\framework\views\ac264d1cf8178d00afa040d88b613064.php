<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Daily Break')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Daily Breaks')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Daily Break')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All Daily Breaks')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-10">
        <form action="<?php echo e(route('administration.daily_break.index')); ?>" method="get" autocomplete="off">
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-6">
                            <label for="user_id" class="form-label"><?php echo e(__('Select Employee')); ?></label>
                            <select name="user_id" id="user_id" class="select2 form-select <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->user_id) ? 'selected' : ''); ?>><?php echo e(__('Select Employee')); ?></option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($user->id); ?>" <?php echo e($user->id == request()->user_id ? 'selected' : ''); ?>>
                                        <?php echo e(get_employee_name($user)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['announcer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-3">
                            <label class="form-label"><?php echo e(__('Breaks Of')); ?></label>
                            <input type="text" name="created_month_year" value="<?php echo e(request()->created_month_year ?? old('created_month_year')); ?>" class="form-control month-year-picker" placeholder="MM yyyy" tabindex="-1"/>
                            <?php $__errorArgs = ['created_month_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-3">
                            <label for="type" class="form-label"><?php echo e(__('Select Break Type')); ?></label>
                            <select name="type" id="type" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default">
                                <option value="" <?php echo e(is_null(request()->type) ? 'selected' : ''); ?>><?php echo e(__('Select Type')); ?></option>
                                <option value="Short" <?php echo e(request()->type == 'Short' ? 'selected' : ''); ?>><?php echo e(__('Short Break')); ?></option>
                                <option value="Long" <?php echo e(request()->type == 'Long' ? 'selected' : ''); ?>><?php echo e(__('Long Break')); ?></option>
                            </select>
                            <?php $__errorArgs = ['announcer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="col-md-12 text-end">
                        <?php if(request()->user_id || request()->created_month_year || request()->type): ?>
                            <a href="<?php echo e(route('administration.daily_break.index')); ?>" class="btn btn-danger confirm-warning">
                                <span class="tf-icon ti ti-refresh ti-xs me-1"></span>
                                <?php echo e(__('Reset Filters')); ?>

                            </a>
                        <?php endif; ?>
                        <button type="submit" name="filter_breaks" value="true" class="btn btn-primary">
                            <span class="tf-icon ti ti-filter ti-xs me-1"></span>
                            <?php echo e(__('Filter Breaks')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                
                <h5 class="mb-0">
                    <span>Daily</span>
                    <span><?php echo e(request()->type ?? request()->type); ?></span>
                    <span>Breaks</span>
                    <span>of</span>
                    <span class="text-bold"><?php echo e(request()->user_id ? show_user_data(request()->user_id, 'name') : 'All Users'); ?></span>
                    <sup>(<b>Month: </b> <?php echo e(request()->created_month_year ? request()->created_month_year : date('F Y')); ?>)</sup>
                </h5>

                <div class="card-header-elements ms-auto">
                    <?php if($dailyBreaks->count() > 0): ?>
                        <a href="<?php echo e(route('administration.daily_break.export', [
                            'user_id' => request('user_id'),
                            'created_month_year' => request('created_month_year'),
                            'type' => request('type'),
                            'filter_breaks' => request('filter_breaks')
                        ])); ?>" target="_blank" class="btn btn-sm btn-dark">
                            <span class="tf-icon ti ti-download me-1"></span>
                            <?php echo e(__('Download')); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Date</th>
                                <th>Name</th>
                                <th>Break Started</th>
                                <th>Break Stopped</th>
                                <th>Total</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $dailyBreaks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $break): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <th>#<?php echo e(serial($dailyBreaks, $key)); ?></th>
                                    <td>
                                        <b class="text-dark"><?php echo e(show_date($break->date)); ?></b>
                                        <br>
                                        <small class="text-bold badge bg-<?php echo e($break->type === 'Short' ? 'primary' : 'warning'); ?>"><?php echo e($break->type); ?> Break</small>
                                    </td>
                                    <td>
                                        <?php echo show_user_name_and_avatar($break->user, role: null); ?>

                                    </td>
                                    <td>
                                        <div class="d-grid">
                                            <span class="text-bold text-dark"><?php echo e(show_time($break->break_in_at)); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if(isset($break->break_out_at)): ?>
                                            <span class="text-bold text-dark"><?php echo e(show_time($break->break_out_at)); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-label-danger text-bold" title="Break Running"><?php echo e(__('Running')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if(isset($break->total_time)): ?>
                                            <?php
                                                if (is_null($break->over_break)) {
                                                    $color = 'success';
                                                } else {
                                                    $color = 'warning';
                                                }
                                            ?>
                                            <span class="text-bold text-<?php echo e($color); ?>"><?php echo e(total_time($break->total_time)); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-label-danger text-bold" title="Break Running"><?php echo e(__('Running')); ?></span>
                                        <?php endif; ?>
                                        <?php if(isset($break->over_break)): ?>
                                            <br>
                                            <small class="text-danger text-bold" title="Total Over Break">
                                                <?php echo e(total_time($break->over_break)); ?>

                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Break Delete')): ?>
                                            <a href="<?php echo e(route('administration.daily_break.destroy', ['break' => $break])); ?>" class="btn btn-sm btn-icon btn-danger confirm-danger" data-bs-toggle="tooltip" title="Delete Break?">
                                                <i class="text-white ti ti-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('administration.daily_break.show', ['break' => $break])); ?>" class="btn btn-sm btn-icon btn-primary item-edit" data-bs-toggle="tooltip" title="Show Details">
                                            <i class="ti ti-info-hexagon"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });

            $('.month-year-picker').datepicker({
                format: 'MM yyyy',         // Display format to show full month name and year
                minViewMode: 'months',     // Only allow month selection
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/daily_break/index.blade.php ENDPATH**/ ?>